#!/usr/bin/env python3
"""
Test script to verify Mastodon integration functionality
"""

import os
import sys
import django

# Add the project root to Python path
sys.path.append('/Users/<USER>/Desktop/Flowkar')

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from mastodon.mastodon_auth import get_mastodon_user_info, register_mastodon_app, generate_mastodon_auth_url
from Authentication.models import ThirdPartyAuth, Brands
from Project.views import *

def test_mastodon_user_info():
    """Test the get_mastodon_user_info function"""
    print("Testing Mastodon user info retrieval...")
    
    # Test with a known Mastodon instance and token (you'll need to replace with actual values)
    instance_url = 'https://mastodon.social'
    access_token = 'your_test_token_here'  # Replace with actual token for testing
    
    # Test the function
    user_info = get_mastodon_user_info(instance_url, access_token)
    
    if user_info:
        print("✅ Mastodon user info retrieved successfully:")
        print(f"   - ID: {user_info.get('id')}")
        print(f"   - Username: {user_info.get('username')}")
        print(f"   - Display Name: {user_info.get('display_name')}")
        print(f"   - URL: {user_info.get('url')}")
        print(f"   - Followers: {user_info.get('followers_count')}")
        print(f"   - Following: {user_info.get('following_count')}")
        print(f"   - Posts: {user_info.get('statuses_count')}")
    else:
        print("❌ Failed to retrieve Mastodon user info")
    
    return user_info is not None

def test_mastodon_app_registration():
    """Test Mastodon app registration"""
    print("\nTesting Mastodon app registration...")
    
    instance_url = 'https://mastodon.social'
    app_data = register_mastodon_app(instance_url)
    
    if app_data and 'client_id' in app_data and 'client_secret' in app_data:
        print("✅ Mastodon app registration successful:")
        print(f"   - Client ID: {app_data['client_id'][:20]}...")
        print(f"   - Client Secret: {app_data['client_secret'][:20]}...")
        print(f"   - Instance URL: {app_data['instance_url']}")
        return True
    else:
        print("❌ Failed to register Mastodon app")
        return False

def test_mastodon_auth_url():
    """Test Mastodon auth URL generation"""
    print("\nTesting Mastodon auth URL generation...")
    
    instance_url = 'https://mastodon.social'
    client_id = 'test_client_id'
    state = 'test_state'
    
    auth_url = generate_mastodon_auth_url(instance_url, client_id, state)
    
    if auth_url and 'oauth/authorize' in auth_url:
        print("✅ Mastodon auth URL generated successfully:")
        print(f"   - URL: {auth_url}")
        return True
    else:
        print("❌ Failed to generate Mastodon auth URL")
        return False

def test_database_model():
    """Test that the database model has the required fields"""
    print("\nTesting database model...")
    
    try:
        # Check if the mastodon_instance_url field exists
        from django.db import connection
        cursor = connection.cursor()
        cursor.execute("PRAGMA table_info(Authentication_thirdpartyauth)")
        columns = [column[1] for column in cursor.fetchall()]
        
        required_fields = ['mastodon_check', 'mastodon_token', 'mastodon_user_id', 'mastodon_instance_url']
        missing_fields = [field for field in required_fields if field not in columns]
        
        if not missing_fields:
            print("✅ All required Mastodon fields exist in database model")
            return True
        else:
            print(f"❌ Missing fields in database model: {missing_fields}")
            return False
            
    except Exception as e:
        print(f"❌ Error checking database model: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Mastodon Integration Tests\n")
    
    tests = [
        test_database_model,
        test_mastodon_app_registration,
        test_mastodon_auth_url,
        # test_mastodon_user_info,  # Commented out as it requires a valid token
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)
    
    print(f"\n📊 Test Results: {sum(results)}/{len(results)} tests passed")
    
    if all(results):
        print("🎉 All tests passed! Mastodon integration is ready.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")

if __name__ == "__main__":
    main()
